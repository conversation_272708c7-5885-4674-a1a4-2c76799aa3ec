using Assets.Scripts.CasualTools.Dialogs;
using Assets.Scripts.MapScene;
using Assets.Scripts.Xiaoming.UI;
using I2.Loc;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class ActivityButton : MonoBehaviour
{
    public Text text;

    // Start is called before the first frame update
    void Start()
    {
        UpdateChangeLang();

        //EventManager.RegisterEvent(LangConfig.ON_CHANGE_LANG_EVENT, new Action(this.UpdateChangeLang));
    }

    void UpdateChangeLang()
    {
        //switch (LangConfig.Current.CurLangCode)
        //{
        //    case "cn":
        //        text.text = "�����";
        //        break;
        //    case "en":
        //        text.text = "Activity Center";
        //        break;
        //    case "zh":
        //        text.text = "�������";
        //        break;
        //}
    }

    public void OnClick()
    {
        if (!USDKUtils.UCommon.ShowActivityPage())
        {
            string errorMessage = ScriptLocalization.Get("ActivityNotOpen");
            Vector3 pos = Vector3.down * 0.5f;
            MapManager.Instance.ShowErrorMessageAtPosition(errorMessage, pos);
        }
    }

    public void OnOpenLuckwheel()
    {
        GameObject obj = DialogManager.Instance.ShowDialog(DialogLibrary.Instance.LuckwheelDialog, false);
        //ItemGetDialog itemGetDialog = obj.GetComponent<ItemGetDialog>();
        //itemGetDialog.SetRewards(dicRewards);
    }

}
