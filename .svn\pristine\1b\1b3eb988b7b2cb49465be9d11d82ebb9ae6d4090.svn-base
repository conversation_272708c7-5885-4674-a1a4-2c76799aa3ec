﻿using UnityEngine;
using TMPro;

[ExecuteAlways]
public class TextArcEffect : MonoBehaviour
{
    public float arcHeight = 5.0f;  // 弧线的高度
    private TMP_Text textMeshPro;

    void Awake()
    {
        textMeshPro = GetComponent<TMP_Text>();
    }

    void Update()
    {
        if (textMeshPro == null) return;

        textMeshPro.ForceMeshUpdate();

        TMP_TextInfo textInfo = textMeshPro.textInfo;
        int characterCount = textInfo.characterCount;

        if (characterCount == 0) return;

        Vector3[] vertices;
        for (int i = 0; i < characterCount; i++)
        {
            if (!textInfo.characterInfo[i].isVisible)
                continue;

            vertices = textInfo.meshInfo[textInfo.characterInfo[i].materialReferenceIndex].vertices;

            for (int j = 0; j < 4; j++)
            {
                Vector3 orig = vertices[textInfo.characterInfo[i].vertexIndex + j];
                float offset = Mathf.Sin((orig.x + textMeshPro.rectTransform.rect.width / 2) / textMeshPro.rectTransform.rect.width * Mathf.PI) * arcHeight;
                vertices[textInfo.characterInfo[i].vertexIndex + j] = new Vector3(orig.x, orig.y + offset, orig.z);
            }
        }

        textMeshPro.UpdateVertexData(TMP_VertexDataUpdateFlags.Vertices);
    }
}
