﻿Shader "UI/ProgressBar_Fixed"
{
    Properties
    {
        [PerRendererData] _MainTex ("Sprite Texture", 2D) = "white" {}
        _Color ("Tint", Color) = (1,1,1,1)
        _Progress ("Progress", Range(0,1)) = 0
        _SmoothFactor ("平滑因子", Range(0.001, 0.1)) = 0.01
        _Direction ("方向", Float) = 0 // 0=从左到右，1=从右到左，2=从下到上，3=从上到下
        [Toggle] _StrictBounds ("严格边界", Float) = 0
    }

    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }

        Cull Off
        Lighting Off
        ZWrite Off
        Blend One OneMinusSrcAlpha

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            struct appdata_t
            {
                float4 vertex   : POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                fixed4 color    : COLOR;
                half2 texcoord  : TEXCOORD0;
                float progress  : TEXCOORD1;
                float direction : TEXCOORD2;
                float strict    : TEXCOORD3;
            };

            fixed4 _Color;
            float _Progress;
            float _SmoothFactor;
            float _Direction;
            float _StrictBounds;

            v2f vert(appdata_t IN)
            {
                v2f OUT;
                OUT.vertex = UnityObjectToClipPos(IN.vertex);
                OUT.color = IN.color * _Color;
                OUT.texcoord = IN.texcoord;
                OUT.progress = _Progress;
                OUT.direction = _Direction;
                OUT.strict = _StrictBounds;
                return OUT;
            }

            sampler2D _MainTex;

            fixed4 frag(v2f IN) : SV_Target
            {
                // 确保UV坐标在有效范围内
                float2 clampedUV = saturate(IN.texcoord);
                float progressValue = IN.progress;
                float2 uvToCheck = clampedUV;
                
                // 根据方向调整UV检查
                if (IN.direction >= 0.5 && IN.direction < 1.5) // 从右到左
                    uvToCheck.x = 1 - clampedUV.x;
                else if (IN.direction >= 1.5 && IN.direction < 2.5) // 从下到上
                    uvToCheck.x = clampedUV.y;
                else if (IN.direction >= 2.5 && IN.direction <= 3.5) // 从上到下
                    uvToCheck.x = 1 - clampedUV.y;
                
                // 处理进度条完全填满的情况
                if (progressValue >= 0.999)
                {
                    fixed4 c = tex2D(_MainTex, clampedUV) * IN.color;
                    c.rgb *= c.a;
                    return c;
                }
                
                // 严格边界模式下不使用平滑过渡
                if (IN.strict > 0.5)
                {
                    if (uvToCheck.x > progressValue)
                        discard;
                }
                else
                {
                    // 使用平滑阶跃函数替代直接裁剪
                    float alpha = step(uvToCheck.x, progressValue + _SmoothFactor) - 
                                step(uvToCheck.x, progressValue - _SmoothFactor);
                    if (alpha < 0.05)
                        discard;
                }

                fixed4 c = tex2D(_MainTex, clampedUV) * IN.color;
                c.rgb *= c.a;
                return c;
            }
            ENDCG
        }
    }
}