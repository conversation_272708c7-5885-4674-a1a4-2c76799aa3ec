using Assets.Scripts.DataHelpers;
using Assets.Scripts.Dialogs;
using Assets.Scripts.GamePlayScene.Mechanics.Items;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;

public class LuckwheelDialog : FullscreenDialog
{
    /// <summary> 金币数量显示 </summary>
    public TextMeshPro CoinLabel;
    /// <summary> 轮盘 </summary>
    public Transform wheel;
    /// <summary> 广告进度 </summary>
    public SpriteRenderer Progress;
    /// <summary> 广告进度显示 </summary>
    public TextMeshPro BoxProgress;
    /// <summary> 起始角速度 </summary>
    public float startSpeed = 800f;
    /// <summary> 减速系数 </summary>
    public float deceleration = 300f;

    private int _current = 0;
    /// <summary> 转盘数据 </summary>
    private List<CoinData> items = new List<CoinData>();
    /// <summary> 转盘显示数据 </summary>
    private List<TextMeshPro> textMeshPros = new List<TextMeshPro>();
    /// <summary> 是否正在抽奖 </summary>
    private bool isSpinning = false;
    public int extraRoundsMin = 3;
    public int extraRoundsMax = 5;

    private void Awake()
    {
        UpdateWheelData();
        // 获取转盘下所有子节点的TextMeshPro组件
        TextMeshPro[] textComponents = wheel.GetComponentsInChildren<TextMeshPro>();
        if (textComponents.Length > 0)
        {
            foreach (var text in textComponents)
            {
                textMeshPros.Add(text);
            }
        }
        else
        {
            Debug.LogError("转盘下没有找到TextMeshPro组件，请检查转盘的子节点。");
        }
        // 初始化转盘文本
        for (int i = 0; i < items.Count; i++)
        {
            if (i < textMeshPros.Count)
            {
                textMeshPros[i].text = items[i].count.ToString();
            }
            else
            {
                Debug.LogWarning($"转盘数据数量 ({items.Count}) 超过了文本组件数量 ({textMeshPros.Count})，请检查转盘配置。");
            }
        }
    }

    /// <summary>
    /// 更新转盘数据
    /// </summary>
    private void UpdateWheelData()
    {
        var sourceData = GameVersionManager.Instance.wheelDataList;
        items.Clear();
        foreach (var item in sourceData)
        {
            items.Add(new CoinData
            {
                count = InventoryHelper.Instance.HandleSubsequentLevelReward(item.count),
                weight = item.weight
            });
        }
        if (items.Count == 0)
        {
            Debug.LogError("转盘数据列表为空，请检查GameVersionManager中的wheelDataList是否已正确配置。");
        }
    }

    private void Update()
    {
        Refresh();
    }

    private void Refresh()
    {
        if (InventoryHelper.Instance.CacheNewCoin != this._current)
        {
            this._current = InventoryHelper.Instance.CacheNewCoin;
            this.CoinLabel.text = InventoryHelper.Instance.CacheNewCoin.ToString();
        }
    }
    #region 转盘旋转事件
    public void Spin()
    {
        if (isSpinning || items.Count == 0)
            return;

        isSpinning = true;

        int selectedIndex = GetWeightedRandomIndex();
        float anglePerSegment = 360f / items.Count;

        // 顺时针绘制时，目标角度 = 360 - 区域中点角度
        float targetAngle = 360f - (selectedIndex * anglePerSegment + anglePerSegment / 2f);

        int extraRounds = Random.Range(extraRoundsMin, extraRoundsMax + 1);
        float totalRotation = extraRounds * 360f + targetAngle;

        StartCoroutine(RotateWheel(totalRotation, selectedIndex, targetAngle));
    }

    int GetWeightedRandomIndex()
    {
        int totalWeight = 0;
        foreach (var item in items)
            totalWeight += item.weight;

        int r = Random.Range(0, totalWeight);
        int sum = 0;
        for (int i = 0; i < items.Count; i++)
        {
            sum += items[i].weight;
            if (r < sum)
                return i;
        }
        return items.Count - 1;
    }

    IEnumerator RotateWheel(float totalAngle, int selectedIndex, float targetAngle)
    {
        float rotated = 0f;
        float speed = startSpeed;

        while (rotated < totalAngle)
        {
            float delta = speed * Time.deltaTime;
            rotated += delta;
            wheel.Rotate(0f, 0f, -delta); // 顺时针

            if (speed > 100f)
                speed -= deceleration * Time.deltaTime;

            yield return null;
        }

        // 🧠 平滑补正角度（顺时针）
        float startZ = wheel.eulerAngles.z % 360f;
        float endZ = targetAngle % 360f;
        float totalOffset = (endZ - startZ + 360f) % 360f;

        float duration = 0.4f;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.SmoothStep(0f, 1f, elapsed / duration);
            float currentZ = startZ + totalOffset * t;
            wheel.eulerAngles = new Vector3(0f, 0f, currentZ % 360f);
            yield return null;
        }

        wheel.eulerAngles = new Vector3(0f, 0f, endZ); // 最终强制对齐

        isSpinning = false;
        Debug.Log($"✅ 中奖：第 {selectedIndex} 项 (count={items[selectedIndex].count})");
    }
    #endregion

    /// <summary>
    /// 观看广告开始转动轮盘
    /// </summary>
    public void ShowADStartWheel()
    {
        Debug.Log("观看广告开始转动轮盘");
        Debug.Log("开始转动轮盘");
        Spin();
    }

    /// <summary>
    /// 开启宝箱
    /// </summary>
    public void OpenBox()
    {
        Debug.Log("开启宝箱");
    }

}

class CoinData
{
    public int count;
    public int weight;
}