using Assets.Scripts.DataHelpers;
using Assets.Scripts.Dialogs;
using Assets.Scripts.GamePlayScene.Mechanics.Items;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;

public class LuckwheelDialog : FullscreenDialog
{
    /// <summary> 金币数量显示 </summary>
    public TextMeshPro CoinLabel;
    /// <summary> 轮盘 </summary>
    public Transform wheel;
    /// <summary> 广告进度 </summary>
    public SpriteRenderer Progress;
    /// <summary> 广告进度显示 </summary>
    public TextMeshPro BoxProgress;
    /// <summary> 起始角速度 </summary>
    public float startSpeed = 800f;
    /// <summary> 减速系数 </summary>
    public float deceleration = 300f;

    private int _current = 0;
    /// <summary> 转盘数据 </summary>
    private List<CoinData> items = new List<CoinData>();
    /// <summary> 转盘显示数据 </summary>
    private List<TextMeshPro> textMeshPros = new List<TextMeshPro>();
    /// <summary> 原始转盘数据（用于计算实际奖励） </summary>
    private List<WheelData> originalWheelData = new List<WheelData>();
    /// <summary> 是否正在抽奖 </summary>
    private bool isSpinning = false;
    public int extraRoundsMin = 3;
    public int extraRoundsMax = 5;

    private void Awake()
    {
        UpdateWheelData();
        // 获取转盘下所有子节点的TextMeshPro组件
        TextMeshPro[] textComponents = wheel.GetComponentsInChildren<TextMeshPro>();
        if (textComponents.Length > 0)
        {
            foreach (var text in textComponents)
            {
                textMeshPros.Add(text);
            }
        }
        else
        {
            Debug.LogError("转盘下没有找到TextMeshPro组件，请检查转盘的子节点。");
        }
        // 初始化转盘文本
        for (int i = 0; i < items.Count; i++)
        {
            if (i < textMeshPros.Count)
            {
                textMeshPros[i].text = items[i].count.ToString();
            }
            else
            {
                Debug.LogWarning($"转盘数据数量 ({items.Count}) 超过了文本组件数量 ({textMeshPros.Count})，请检查转盘配置。");
            }
        }
    }

    /// <summary>
    /// 更新转盘数据
    /// </summary>
    private void UpdateWheelData()
    {
        var sourceData = GameVersionManager.Instance.wheelDataList;
        items.Clear();
        originalWheelData.Clear();

        foreach (var item in sourceData)
        {
            // 保存原始数据
            originalWheelData.Add(item);

            // 计算显示用的金币数量
            items.Add(new CoinData
            {
                count = InventoryHelper.Instance.HandleSubsequentLevelReward(item.count),
                weight = item.weight
            });
        }
        if (items.Count == 0)
        {
            Debug.LogError("转盘数据列表为空，请检查GameVersionManager中的wheelDataList是否已正确配置。");
        }
    }

    private void Update()
    {
        Refresh();
    }

    private void Refresh()
    {
        if (InventoryHelper.Instance.CacheNewCoin != this._current)
        {
            this._current = InventoryHelper.Instance.CacheNewCoin;
            this.CoinLabel.text = InventoryHelper.Instance.CacheNewCoin.ToString();
        }
    }
    #region 转盘旋转事件
    public void Spin()
    {
        if (isSpinning || items.Count == 0)
            return;

        isSpinning = true;

        int selectedIndex = GetWeightedRandomIndex();
        float anglePerSegment = 360f / items.Count;

        // 顺时针绘制时，目标角度 = 360 - 区域中点角度
        float targetAngle = 360f - (selectedIndex * anglePerSegment + anglePerSegment / 2f);

        int extraRounds = Random.Range(extraRoundsMin, extraRoundsMax + 1);
        float totalRotation = extraRounds * 360f + targetAngle;

        StartCoroutine(RotateWheel(totalRotation, selectedIndex, targetAngle));
    }

    int GetWeightedRandomIndex()
    {
        int totalWeight = 0;
        foreach (var item in items)
            totalWeight += item.weight;

        int r = Random.Range(0, totalWeight);
        int sum = 0;
        for (int i = 0; i < items.Count; i++)
        {
            sum += items[i].weight;
            if (r < sum)
                return i;
        }
        return items.Count - 1;
    }

    IEnumerator RotateWheel(float totalAngle, int selectedIndex, float targetAngle)
    {
        float rotated = 0f;
        float speed = startSpeed;

        while (rotated < totalAngle)
        {
            float delta = speed * Time.deltaTime;
            rotated += delta;
            wheel.Rotate(0f, 0f, -delta); // 顺时针

            if (speed > 100f)
                speed -= deceleration * Time.deltaTime;

            yield return null;
        }

        // 修复角度补正逻辑，避免往回转
        float currentZ = wheel.eulerAngles.z;
        float finalZ = targetAngle;

        // 确保最终角度在0-360范围内
        while (finalZ < 0) finalZ += 360f;
        while (finalZ >= 360f) finalZ -= 360f;

        // 计算最短路径到目标角度
        float angleDiff = finalZ - currentZ;
        if (angleDiff > 180f) angleDiff -= 360f;
        if (angleDiff < -180f) angleDiff += 360f;

        // 如果角度差异很小，直接设置最终角度
        if (Mathf.Abs(angleDiff) < 5f)
        {
            wheel.eulerAngles = new Vector3(0f, 0f, finalZ);
        }
        else
        {
            // 平滑补正到最终角度
            float duration = 0.3f;
            float elapsed = 0f;
            float startAngle = currentZ;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = Mathf.SmoothStep(0f, 1f, elapsed / duration);
                float currentAngle = startAngle + angleDiff * t;
                wheel.eulerAngles = new Vector3(0f, 0f, currentAngle);
                yield return null;
            }

            wheel.eulerAngles = new Vector3(0f, 0f, finalZ);
        }

        isSpinning = false;

        // 给予实际奖励并显示正确的中奖信息
        int actualReward = items[selectedIndex].count;
        InventoryHelper.Instance.AddItemAmount(InventoryItemType.NewCoin, actualReward, true);

        Debug.Log($"✅ 中奖：第 {selectedIndex} 项 (显示={textMeshPros[selectedIndex].text}, 实际奖励={actualReward})");
    }
    #endregion

    /// <summary>
    /// 观看广告开始转动轮盘
    /// </summary>
    public void ShowADStartWheel()
    {
        Debug.Log("观看广告开始转动轮盘");
        Debug.Log("开始转动轮盘");
        Spin();
    }

    /// <summary>
    /// 开启宝箱
    /// </summary>
    public void OpenBox()
    {
        Debug.Log("开启宝箱");
    }

}

class CoinData
{
    public int count;
    public int weight;
}