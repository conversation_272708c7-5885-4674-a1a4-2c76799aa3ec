// dnSpy decompiler from Assembly-CSharp.dll class: Assets.Scripts.CasualTools.Dialogs.DialogManager
using Assets.Scripts.CasualTools.Common.Logging;
using Assets.Scripts.CasualTools.Common.Pooling;
using Assets.Scripts.CasualTools.Common.Tasks;
using Assets.Scripts.Logging;
using Assets.Scripts.Utils;
using DG.Tweening;
using System;
using System.Collections;
using System.Collections.Generic;
using Ui.VerticalScroll;
using UnityEngine;
using Utils;

namespace Assets.Scripts.CasualTools.Dialogs
{
    public class DialogManager
    {
        private DialogManager()
        {
        }

        public static DialogManager Instance
        {
            get
            {
                if (DialogManager._instance == null)
                {
                    DialogManager._instance = new DialogManager();
                }
                return DialogManager._instance;
            }
        }

        public bool InputsBlocked { get; set; }

        public GameObject ShowDialog(GameObject dialogPrefab, bool showAfterActiveIsClosed, bool showLighterBackground = false, bool readFromPool = false, Action<Dialog> runBeforePlayIn = null, bool showDarkBackground = true)
        {
            //xiaoming ShowDialog
            LogManager.Debug(LogTags.DialogManager, "Show Dialog:" + dialogPrefab, new object[0]);
            VerticalScrollController.LockAllScrolls();
            VerticalScrollController.InvalidateScrolls();
            if (showDarkBackground)
            {
                DialogLibrary.Instance.ShowDialogBackground(showLighterBackground, 0.12f, 0f);
            }
            this.AreThereActiveDialogs = true;
            Sequence sequence = null;
            GameObject gameObject = (!readFromPool) ? UnityEngine.Object.Instantiate<GameObject>(dialogPrefab) : dialogPrefab.Spawn();
            Dialog component = gameObject.GetComponent<Dialog>();
            component.DialogCreated();
            DialogManager.ScaleDialog(component);
            if (this._currentDialog != null && showAfterActiveIsClosed)
            {
                this._dialogs.Push(component);
                return gameObject;
            }
            if (this._currentDialog != null)
            {
                if (this._currentDialog.FrameCreated == Time.frameCount)
                {
                    this._currentDialog.PlayDialogOutFast();
                }
                else
                {
                    sequence = this._currentDialog.PlayDialogOutAnimation();
                }
                this._dialogs.Push(this._currentDialog);
            }
            this._currentDialog = component;
            if (runBeforePlayIn != null)
            {
                runBeforePlayIn(this._currentDialog);
            }
            if (sequence == null)
            {
                this._currentDialog.PlayDialogInAnimation();
            }
            else
            {
                sequence.OnComplete(delegate
                {
                    this._currentDialog.PlayDialogInAnimation();
                });
            }
            this._currentDialog.SetDialogBounds();
            return gameObject;
        }

        public Dialog GetActiveDialog()
        {
            return this._currentDialog;
        }

        public static void ScaleDialog(Dialog theDialog)
        {
            float width = CameraHelper.Width;
            float height = CameraHelper.Height;
            float b;
            if (CameraHelper.IsIpad)
            {
                b = width / theDialog.IPadReferenceWidth;
            }
            else
            {
                b = width / theDialog.ReferenceWidth;
            }
            float a = height / theDialog.ReferenceHeight;
            float num = Mathf.Min(a, b);
            theDialog.gameObject.transform.localScale = new Vector3(num, num, num);
        }

        public void ClearStackWithoutAnimation()
        {
            while (this._dialogs.Count > 0)
            {
                Dialog dialog = this._dialogs.Pop();
                if (!(dialog == null))
                {
                    UnityEngine.Object.Destroy(dialog.gameObject);
                }
            }
            if (DialogLibrary.Instance != null)
            {
                DialogLibrary.Instance.HideDialogBackground();
            }
            if (this._currentDialog == null)
            {
                this.AreThereActiveDialogs = false;
            }
        }

        public void ClearAll(bool force = false)
        {
            while (this._dialogs.Count > 0)
            {
                Dialog dialog = this._dialogs.Pop();
                if (!(dialog == null))
                {
                    UnityEngine.Object.Destroy(dialog.gameObject);
                }
            }
            if (this._currentDialog != null && (force || this._currentDialog.ShouldBeClosedOnSceneChange()))
            {
                if (DialogLibrary.Instance != null)
                {
                    DialogLibrary.Instance.HideDialogBackground();
                }
                UnityEngine.Object.Destroy(this._currentDialog.gameObject);
            }
            this._currentDialog = null;
            this.Reset();
        }

        public void CloseDialog(Dialog dialog, bool isForce = false)
        {
            if (dialog != null && this._currentDialog == dialog)
            {
                LogManager.Debug(LogTags.DialogManager, "Close arrived for:" + dialog, new object[0]);
                this.KillDialog(dialog);
            }
            else if (dialog != null && this._currentDialog != dialog && isForce)
            {
                this.KillDialog(dialog, isForce);
            }
            else
            {
                LogManager.Warning(LogTags.DialogManager, "Can not close dialog! Not latest dialog.", new object[0]);
            }
            //if(dialog!=null && dialog.isActiveAndEnabled)
        }

        public void CloseCurrentDialog()
        {
            if (this._currentDialog != null)
            {
                this.CloseDialog(this._currentDialog);
            }
        }

        private void KillDialog(Dialog dialog, bool isForce = false)
        {
            Sequence sequence = dialog.PlayDialogOutAnimation();
            if (isForce)
            {
                //if()
            }
            else
            {
                this._currentDialog = null;
            }
            if (sequence != null)
            {
                sequence.OnComplete(delegate
                {
                    this.ShowOldDialog(dialog);
                });
            }
            else
            {
                this.ShowOldDialog(dialog);
            }
        }

        private void ShowOldDialog(Dialog dialog)
        {
            if (this._dialogs.Count <= 0)
            {
                this.AreThereActiveDialogs = false;
                VerticalScrollController.UnlockAllScrolls();
            }
            DialogLibrary.Instance.HideDialogBackground();
            dialog.OnDialogClosed();
            UnityEngine.Object.Destroy(dialog.gameObject);
            if (this._currentDialog != null || !this.AreThereActiveDialogs)
            {
                return;
            }
            if (this._dialogs.Count <= 0)
            {
                return;
            }
            this._currentDialog = this._dialogs.Pop();
            this._currentDialog.SetDialogBounds();
            DialogLibrary.Instance.ShowDialogBackground(false, 0.12f, 0f);
            this._currentDialog.PlayDialogInAnimation();
        }

        public void Reset()
        {
            this._currentDialog = null;
            this._dialogs.Clear();
            this.AreThereActiveDialogs = false;
        }

        public void RescaleDialogs()
        {
            if (this._currentDialog != null)
            {
                DialogManager.ScaleDialog(this._currentDialog);
            }
            foreach (Dialog theDialog in this._dialogs)
            {
                DialogManager.ScaleDialog(theDialog);
            }
        }

        public void WaitDialogLibraryAndTakeAction(Action action)
        {
            if (DialogLibrary.Instance)
            {
                action();
            }
            else
            {
                new Task(DialogManager.TakeActionAfterDialogLibraryInitialized(action));
            }
        }

        private static IEnumerator TakeActionAfterDialogLibraryInitialized(Action action)
        {
            while (DialogLibrary.Instance == null)
            {
                yield return null;
            }
            action();
            yield break;
        }

        private static DialogManager _instance;

        private readonly Stack<Dialog> _dialogs = new Stack<Dialog>();

        private Dialog _currentDialog;

        public bool AreThereActiveDialogs;
    }
}